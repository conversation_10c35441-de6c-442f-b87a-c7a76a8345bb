import axios from 'axios'

class DifyApiService {
  constructor(apiUrl, apiKey) {
    this.apiUrl = apiUrl
    this.apiKey = apiKey
    this.client = axios.create({
      baseURL: apiUrl,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })
  }

  /**
   * 发送聊天消息（流式响应）
   * @param {string} query - 用户输入的消息
   * @param {string} user - 用户标识
   * @param {string} conversationId - 会话ID（可选）
   * @param {Object} inputs - 额外输入参数（可选）
   * @param {Function} onMessage - 流式消息回调函数
   * @param {Function} onComplete - 完成回调函数
   * @param {Function} onError - 错误回调函数
   * @returns {Promise} API响应
   */
  async sendMessageStream(query, user, conversationId = null, inputs = {},files = [], onMessage, onComplete, onError) {
    try {
      
      const payload = {
        inputs,
        query,
        user,
        response_mode: 'streaming',
        files
      };

      if (conversationId) {
        payload.conversation_id = conversationId;
      }

      // 使用fetch API来处理流式响应
      const response = await fetch(`${this.apiUrl}/chat-messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let partialLine = '';
      let conversationId_new = null;
      let messageId = null;
      let fullResponse = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          // 解码二进制数据
          const text = decoder.decode(value);
          const lines = (partialLine + text).split('\n');
          partialLine = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6); // 移除 "data: " 前缀
                const data = JSON.parse(jsonStr);

                // 新对话时，赋值id
                if (!conversationId_new) {
                  conversationId_new = data.conversation_id;
                }

                // 检查是否是同一条消息的更新
                if (messageId === null) {
                  messageId = data.message_id;
                } else if (data.message_id && data.message_id !== messageId) {
                  continue; // 忽略不同ID的消息
                }

                // 更新系统消息内容
                if (data.event === 'message' && data.answer) {
                  // 累积到完整响应中
                  fullResponse += data.answer;

                  // 提取思考过程和正文
                  const { thinking, cleanedText,isThinking } = this.extractThinking(fullResponse);
                  
                  // 调用消息回调
                  if (onMessage) {
                    onMessage({
                      type: 'message_update',
                      thinking: thinking,
                      isThinking: isThinking,
                      content: cleanedText,
                      fullResponse: fullResponse,
                      conversation_id: conversationId_new,
                      message_id: messageId
                    });
                  }
                }
              } catch (e) {
                console.error('解析流式响应失败:', e);
              }
            }
          }
        }

        // 确保最终消息是完整的
        const { thinking, cleanedText } = this.extractThinking(fullResponse);

        if (onComplete) {
          onComplete({
            conversation_id: conversationId_new,
            message_id: messageId,
            answer: cleanedText.replace(/<!-- thinking-placeholder -->/g, ''),
            thinking: thinking,
            fullResponse: fullResponse
          });
        }

        return {
          conversation_id: conversationId_new,
          message_id: messageId,
          answer: cleanedText.replace(/<!-- thinking-placeholder -->/g, ''),
          thinking: thinking,
          fullResponse: fullResponse
        };
      } catch (error) {
        console.error('读取流时出错:', error);
        if (onError) {
          onError(error);
        }
      }
    } catch (error) {
      console.error('发送流式消息请求失败:', error);
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }

  /**
   * 提取思考过程和正文内容
   * @param {string} fullResponse - 完整响应内容
   * @returns {Object} 包含thinking和cleanedText的对象
   */
  extractThinking(text) {
    let thinking = '';
    let cleanedText = text;
    let isThinking = false;

    // 多种思考过程格式的正则表达式
    const patterns = [
      // 最常见格式: <think>...</think>
      { closed: /<think>([\s\S]*?)<\/think>/g, open: /<think>([\s\S]*?)$/, tag: 'think' },
      // 详细格式: <details>...<summary>Thinking...</summary>...</details>
      { closed: /<details[^>]*>\s*<summary[^>]*>\s*Thinking[^<]*<\/summary>([\s\S]*?)<\/details>/g, open: /<details[^>]*>\s*<summary[^>]*>\s*Thinking[^<]*<\/summary>([\s\S]*?)$/, tag: 'details' },
      // 简化格式: <thinking>...</thinking>
      { closed: /<thinking>([\s\S]*?)<\/thinking>/g, open: /<thinking>([\s\S]*?)$/, tag: 'thinking' },
      // 注释格式: <!-- thinking -->...<!-- /thinking -->
      { closed: /<!-- thinking -->([\s\S]*?)<!-- \/thinking -->/g, open: /<!-- thinking -->([\s\S]*?)$/, tag: 'comment' },
      // 中文格式: <思考>...</思考>
      { closed: /<思考>([\s\S]*?)<\/思考>/g, open: /<思考>([\s\S]*?)$/, tag: '思考' },
      // 方括号格式: [思考]...[/思考]
      { closed: /\[思考\]([\s\S]*?)\[\/思考\]/g, open: /\[思考\]([\s\S]*?)$/, tag: '[思考]' },
      // 英文方括号格式: [Thinking]...[/Thinking]
      { closed: /\[Thinking\]([\s\S]*?)\[\/Thinking\]/g, open: /\[Thinking\]([\s\S]*?)$/, tag: '[Thinking]' }
    ];

    // 首先处理已闭合的思考标签
    for (const pattern of patterns) {
      const matches = [...text.matchAll(pattern.closed)];
      if (matches.length > 0) {
        // 提取所有已闭合的思考过程
        for (const match of matches) {
          if (match[1]) {
            thinking += match[1].trim() + '\n\n';
            // 用占位符替换思考部分
            cleanedText = cleanedText.replace(match[0], '<!-- thinking-placeholder -->');
          }
        }
        // 移除已处理的闭合标签内容
        text = cleanedText;
        break;
      }
    }

    // 然后处理未闭合的思考标签（实时思考过程）
    for (const pattern of patterns) {
      const openMatch = text.match(pattern.open);
      if (openMatch && openMatch[1]) {
        isThinking = true;
        // 提取未闭合标签中的思考内容
        const currentThinking = openMatch[1].trim();
        if (currentThinking) {
          thinking += (thinking ? '\n\n' : '') + currentThinking;
        }
        // 移除未闭合的思考标签和内容
        cleanedText = cleanedText.replace(openMatch[0], '<!-- thinking-placeholder -->');
        break;
      }
    }

    // 清理思考内容
    thinking = thinking.trim();

    return {
      thinking: thinking,
      cleanedText: cleanedText,
      isThinking: isThinking
    };
  }

  /**
   * 发送聊天消息（非流式，兼容旧版本）
   * @param {string} query - 用户输入的消息
   * @param {string} user - 用户标识
   * @param {string} conversationId - 会话ID（可选）
   * @param {Object} inputs - 额外输入参数（可选）
   * @returns {Promise} API响应
   */
  async sendMessage(query, user, conversationId = null, inputs = { post_name: '前端工程师'}) {
    try {
      const payload = {
        inputs,
        query,
        user,
        response_mode: 'blocking' // 非流式模式
      }

      if (conversationId) {
        payload.conversation_id = conversationId
      }

      const response = await this.client.post('/chat-messages', payload)
      return response.data
    } catch (error) {
      console.error('Dify API Error:', error)
      throw this.handleError(error)
    }
  }

 
  /**
   * 获取历史对话列表
   * @param {string} user - 用户标识
   * @param {number} limit - 限制数量
   * @returns {Promise} 历史对话列表
   */
  async getConversationHistoryList(user, limit = 20,last_id) {
    try {
      const response = await this.client.get(`/conversations`, {
        params: {
          user,
          limit,
          last_id
        }
      })
      return response.data
    } catch (error) {
      console.error('Get conversation history list error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 获取特定会话的消息历史
   * @param {string} user - 用户标识
   * @param {string} conversationId - 会话ID
   * @param {string} first_id - 第一条消息ID（用于分页）
   * @returns {Promise} 会话消息历史
   */
  async getConversationHistory(user, conversationId, first_id = null) {
    try {
      const response = await this.client.get(`/messages`, {
        params: {
          user,
          conversation_id: conversationId,
          first_id: first_id
        }
      })
      return response.data
    } catch (error) {
      console.error('Get conversation history error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 获取会话列表
   * @param {string} user - 用户标识
   * @param {number} limit - 限制数量
   * @returns {Promise} 会话列表
   */
  async getConversations(user, limit = 20) {
    try {
      const response = await this.client.get(`/conversations`, {
        params: {
          user,
          limit
        }
      })
      return response.data
    } catch (error) {
      console.error('Get conversations error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 重命名会话
   * @param {string} conversationId - 会话ID
   * @param {string} name - 新名称
   * @param {string} user - 用户标识
   * @returns {Promise} 重命名结果
   */
  async renameConversation(conversationId, name, user) {
    try {
      const response = await this.client.post(`/conversations/${conversationId}/name`, {
        name,
        user
      })
      return response.data
    } catch (error) {
      console.error('Rename conversation error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 删除会话
   * @param {string} conversationId - 会话ID
   * @param {string} user - 用户标识
   * @returns {Promise} 删除结果
   */
  async deleteConversations(conversationId, user) {
    try {
      const response = await this.client.delete(`/conversations/${conversationId}`, {
        data: {
          user
        }
      })
      return response.data
    } catch (error) {
      console.error('Delete conversations error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 获取应用信息
   * @returns {Promise} 应用信息
   */
  async getAppInfo() {
    try {
      const response = await this.client.get('/info')
      return response.data
    } catch (error) {
      console.error('Get app info error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 文件上传
   * @param {string} file - 文件
   * @param {string} user - 用户标识
   * @returns {Promise} 删除结果
   */
  async uploadFile(file, user) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('user', user)

      const response = await this.client.post('/files/upload', formData, formData)
      return response.data
    } catch (error) {
      console.error('Upload file error:', error)
      throw this.handleError(error)
    }
  }

  /**
   * 处理API错误
   * @param {Error} error - 原始错误
   * @returns {Object} 格式化的错误信息
   */
  handleError(error) {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      return {
        type: 'api_error',
        status,
        message: data?.message || '服务器错误',
        code: data?.code || 'UNKNOWN_ERROR'
      }
    } else if (error.request) {
      // 网络错误
      return {
        type: 'network_error',
        message: '网络连接失败，请检查网络设置',
        code: 'NETWORK_ERROR'
      }
    } else {
      // 其他错误
      return {
        type: 'unknown_error',
        message: error.message || '未知错误',
        code: 'UNKNOWN_ERROR'
      }
    }
  }

}

export default DifyApiService


